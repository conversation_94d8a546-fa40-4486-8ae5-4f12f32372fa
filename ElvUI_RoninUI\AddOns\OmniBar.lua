local E = unpack(ElvUI)
local _, Engine = ...

Engine.OmniBar = {}

local omniBar = Engine.OmniBar

local function SetImportedProfile(profileName, data)
	if not data then return end
    if (data.version ~= 1) then return Engine:Print("Invalid version") end

    local profileName = format("RoninUI %s", Engine.Config.Version)
    -- local profile = L["Imported (%s)"]:format(date())

	OmniBar.db.profiles[profileName] = data.profile
	OmniBar.db:SetProfile(profileName)

	-- merge custom spells
	for k, v in pairs(data.customSpells) do
		OmniBar.db.global.cooldowns[k] = nil
		OmniBar.options.args.customSpells.args.spellId.set(nil, k, v)
	end

	OmniBar:OnEnable()
	-- LibStub("AceConfigRegistry-3.0"):NotifyChange("OmniBar")
	-- return true
end

function omniBar:Decode(profileString)
	if not profileString then return end
	local LibDeflate = LibStub:GetLibrary("LibDeflate")
	local decoded = LibDeflate:DecodeForPrint(encoded)
	if (not decoded) then return Engine:Print("OmniBar profile decode error.") end
	local decompressed = LibDeflate:DecompressZlib(decoded)
	if (not decompressed) then return Engine:Print("OmniBar profile decompression error.") end
	local success, deserialized = OmniBar:Deserialize(decompressed)
	if (not success) then return Engine:Print("OmniBar profile deserialization error.") end
	return deserialized
end

function omniBar:SetupProfile(encodedString, profileID)
	if not E:IsAddOnEnabled('OmniBar') then return end
    if not encodedString then return Engine:Print("No profile provided.") end
	if not profileID or profileID == '' then return Engine:Print("No profile id provided.") end

	local profileName = Engine.ProfileData[addon][profileID..'Name']
	local data = omniBar:Decode(encodedString)

	SetImportedProfile(profileName, data)
end
