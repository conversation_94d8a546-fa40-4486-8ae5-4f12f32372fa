name: Release

on:
  push:
    tags:
      - '*'
    paths-ignore:
      - '.github/**'

jobs:
  release:
    runs-on: ubuntu-22.04
    env:
      CF_API_KEY: ${{ secrets.CF_API_KEY }}
      WAGO_API_TOKEN: ${{ secrets.WAGO_API_TOKEN }}
    steps:
      - name: Clone Project
        uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Setup Environment
        run: echo "RELEASE_VERSION=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV
      - name: Release Job
        uses: BigWigsMods/packager@master
