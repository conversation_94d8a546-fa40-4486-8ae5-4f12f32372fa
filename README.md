## Features
- **Integrated Interface:** Enjoy a complete interface within ElvUI, no extra plugins needed.
- **Improved Performance:** Get better gameplay with a streamlined and stable interface.
- **All-in-One Convenience:** No need for multiple addons, everything’s in this optimized package.
- **Sleek Look, Smooth Experience:** Visual appeal that boosts usability and gameplay.
- **Customized to You:** Tailor your gaming experience easily without sacrificing performance.

*Note:*
RoninUI was initially designed for a 2560x1440 screen resolution. If you’re planning to use it on a 1920x1080 screen, you may need to readjust the windows to ensure optimal display.

## Required AddOns
- [ElvUI](https://tukui.org/elvui)
- [Details! Damage Meter](https://www.curseforge.com/wow/addons/details)
- [WindTools (Retail only)](https://www.curseforge.com/wow/addons/elvui-windtools)
- [OmniCD](https://www.curseforge.com/wow/addons/omnicd)

## Installation Steps
1. Make sure to backup your WTF folder. This is a recommended step to make sure you start with a clean profile.
2. Download the RoninUI installer plugin along with the other required addons such as Details Damage Meter and Windtools.
3. Drag the folders to your AddOns folder. Example: `C:\World of Warcraft\_retail_\Interface\AddOns`.
4. Launch World of Warcraft and follow the on-screen installer steps carefully. Make sure you have all required AddOns enabled before entering the in-game world.

## Credits
- **Repooc:** for the help developing the installer plugin
- Special thanks to the **TukUI Discord community** for their invaluable assistance.
