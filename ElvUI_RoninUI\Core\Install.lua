local AddOnName, Engine = ...
local E, L = unpack(ElvUI)
local D = E.Distributor
local PI = E.PluginInstaller

local AS = E:IsAddOnEnabled('AddOnSkins') and unpack(AddOnSkins)

local config = Engine.Config
local hexElvUIBlue = '|cff1785d1'

local function GetDetailsDesc1Text()
	return E:IsAddOnEnabled('Details') and format('%sCurrent Profile:|r %s%s|r|n%s(|rDetails Config %s>|r Options %s>|r Profiles%s)|r', '|cffFFD900', '|cff5CE1E6', Details:GetCurrentProfileName(), hexElvUIBlue, hexElvUIBlue, hexElvUIBlue, hexElvUIBlue) or ''
end

local function GetDetailsDesc2Text()
	return E:IsAddOnEnabled('Details') and format("|cffFFD900This page will setup the Details profile for %s|r.", config.Title) or ''
end

local function GetDetailsDesc3Text()
	if E:IsAddOnEnabled('Details') then
		return not Engine.AddonSkins:isDualEmbedEnabled() and format('%sAddOnSkins|r has been detected and but an option that is required needs to be enabled before importing the %sDetails|r profile.|nClick the %s\"|r%sFix Confict|r%s\"|r button to fix that option and reload the ui to continue importing the %sDetails|r profile.', hexElvUIBlue, hexElvUIBlue, hexElvUIBlue, '|cffFFD900', hexElvUIBlue, hexElvUIBlue) or ''
	else
		return '|cffFF3333WARNING:|r Details! is not enabled to configure.'
	end
end

local function ElvUIProfileDescText()
	return format('%sCurrent Profile:|r %s%s|r|n%s(|rElvUI Config %s>|r Profiles %s>|r Profile Tab%s)|r', '|cffFFD900', '|cff5CE1E6', E.data:GetCurrentProfile(), hexElvUIBlue, hexElvUIBlue, hexElvUIBlue, hexElvUIBlue)
end

local function MRTDesc1Text()
	if E.Retail and E:IsAddOnEnabled('MRT') then
		local curProfile = (not VMRT.Profile or VMRT.Profile == 'default' and 'Default') or VMRT.Profile
		return format("%sCurrent Profile:|r %s%s|r|n%s(|rMRT Config %s>|r Options %s>|r Profiles%s)|r", '|cffFFD900', '|cff5CE1E6', curProfile, hexElvUIBlue, hexElvUIBlue, hexElvUIBlue, hexElvUIBlue)
	else
		return ''
	end
end

local function OmniCDDesc1Text()
	return E:IsAddOnEnabled('OmniCD') and format('%sCurrent Profile:|r %s%s|r|n%s(|rOmniCD Config %s>|r Profiles%s)|r', '|cffFFD900', '|cff5CE1E6', OmniCD[1].DB:GetCurrentProfile(), hexElvUIBlue, hexElvUIBlue, hexElvUIBlue) or ''
end

local function PlaterDesc1Text()
	return E:IsAddOnEnabled('Plater') and format('%sCurrent Profile:|r %s%s|r|n%s(|rPlater Config %s>|r Profiles %s>|r Profile Settings%s)|r', '|cffFFD900', '|cff5CE1E6', Plater.db:GetCurrentProfile(), hexElvUIBlue, hexElvUIBlue, hexElvUIBlue, hexElvUIBlue) or ''
end

local function WeakAuraButtonText(profileID)
	if not profileID then return Engine:Print('Invalid profile id argument.') end
	local profileString = Engine.ProfileData.WeakAuras[profileID..'String']
	if not profileString or profileString == '' then return Engine:Print('No profile string provided.') end
	local doesExist = Engine.WeakAuras:doesAuraExist(profileString)
	local profileNum = tonumber(profileID:match('%d+'))

	if _G.PluginInstallFrame:IsShown() and _G.PluginInstallFrame.Title:GetText() == Engine.InstallerData.Title and _G.PluginInstallFrame.CurrentPage == 11 then
		_G.PluginInstallFrame['Option'..profileNum]:SetText(doesExist and format('%s\n%s(|r%s%s)|r', Engine.ProfileData.WeakAuras[profileID..'ButtonText'], hexElvUIBlue, '|cff99ff33Detected|r', hexElvUIBlue) or Engine.ProfileData.WeakAuras[profileID..'ButtonText'])
	end
end

local function WeakAuraClassButtonText()
	local color = E:ClassColor(E.myclass, true)
	local prettyText = color:WrapTextInColorCode(E.myLocalizedClass)
	local profileString = Engine.ProfileData.WeakAuras.CLASS[E.myclass]
	local doesExist = Engine.WeakAuras:doesAuraExist(profileString)
	local classPackString = format(Engine.ProfileData.WeakAuras.Profile2ButtonText, prettyText)

	if _G.PluginInstallFrame:IsShown() and _G.PluginInstallFrame.Title:GetText() == Engine.InstallerData.Title and _G.PluginInstallFrame.CurrentPage == 11 then
		_G.PluginInstallFrame.Option2:SetText(doesExist and format('%s\n%s(|r%s%s)|r', classPackString, hexElvUIBlue, '|cff99ff33Detected|r', hexElvUIBlue) or classPackString)
	end
end

local function SetupProfileButton(addon, profileID, callback)
	if not addon or not Engine.ProfileData[addon] then return Engine:Print('Invalid addon argument.') end
	if not profileID then return Engine:Print('Invalid profile id argument.') end

	local profileString
	if addon == 'WeakAuras' and profileID == 'Profile2' then
		profileString = Engine.ProfileData.WeakAuras.CLASS[E.myclass]
	else
		profileString = Engine.ProfileData[addon][profileID..'String']
	end

	if not profileString then return Engine:Print('No profile string provided.') end

	Engine[addon]:SetupProfile(profileString, profileID, callback)
end

local function SetupOptionPreview()
	if not PluginInstallFrame.optionPreview then
		PluginInstallFrame.optionPreview = PluginInstallFrame:CreateTexture()
		PluginInstallFrame.optionPreview:SetAllPoints(PluginInstallFrame)
	end
end

local function SetupOptionScripts(script, texture)
	if script == 'onEnter' then
		PluginInstallFrame.optionPreview:SetTexture(texture)
		if texture and texture ~= '' then
			--* Not sure which one is feels better
			-- UIFrameFadeIn(PluginInstallFrame.optionPreview, 0.5, 0, 0.7)
			-- UIFrameFadeOut(PluginInstallFrame.tutorialImage, 0.4, 1, 0)
			-- UIFrameFadeOut(PluginInstallFrame.Desc1, 0.4, 1, 0)
			-- UIFrameFadeOut(PluginInstallFrame.Desc2, 0.4, 1, 0)
			-- UIFrameFadeOut(PluginInstallFrame.Desc3, 0.4, 1, 0)
			-- UIFrameFadeOut(PluginInstallFrame.Desc4, 0.4, 1, 0)
			-- UIFrameFadeOut(PluginInstallFrame.SubTitle, 0.4, 1, 0)

			UIFrameFadeIn(PluginInstallFrame.optionPreview, 0.5, PluginInstallFrame.optionPreview:GetAlpha(), 0.7)
			UIFrameFadeOut(PluginInstallFrame.tutorialImage, 0.4, PluginInstallFrame.tutorialImage:GetAlpha(), 0)
			UIFrameFadeOut(PluginInstallFrame.Title, 0.4, PluginInstallFrame.Title:GetAlpha(), 0)
			UIFrameFadeOut(PluginInstallFrame.Prev, 0.4, PluginInstallFrame.Prev:GetAlpha(), 0)
			UIFrameFadeOut(PluginInstallFrame.Status, 0.4, PluginInstallFrame.Status:GetAlpha(), 0)
			UIFrameFadeOut(PluginInstallFrame.Next, 0.4, PluginInstallFrame.Next:GetAlpha(), 0)
			UIFrameFadeOut(PluginInstallFrame.Desc1, 0.4, PluginInstallFrame.Desc1:GetAlpha(), 0)
			UIFrameFadeOut(PluginInstallFrame.Desc2, 0.4, PluginInstallFrame.Desc2:GetAlpha(), 0)
			UIFrameFadeOut(PluginInstallFrame.Desc3, 0.4, PluginInstallFrame.Desc3:GetAlpha(), 0)
			UIFrameFadeOut(PluginInstallFrame.Desc4, 0.4, PluginInstallFrame.Desc4:GetAlpha(), 0)
			UIFrameFadeOut(PluginInstallFrame.SubTitle, 0.4, PluginInstallFrame.SubTitle:GetAlpha(), 0)
		end
	elseif script == 'onLeave' then
		--* Not sure which one is feels better
		-- UIFrameFadeOut(PluginInstallFrame.optionPreview, 0.5, 0.7, 0)
		-- UIFrameFadeIn(PluginInstallFrame.tutorialImage, 0.4, 0, 1)
		-- UIFrameFadeIn(PluginInstallFrame.Desc1, 0.4, 0, 1)
		-- UIFrameFadeIn(PluginInstallFrame.Desc2, 0.4, 0, 1)
		-- UIFrameFadeIn(PluginInstallFrame.Desc3, 0.4, 0, 1)
		-- UIFrameFadeIn(PluginInstallFrame.Desc4, 0.4, 0, 1)
		-- UIFrameFadeIn(PluginInstallFrame.SubTitle, 0.4, 0, 1)

		UIFrameFadeOut(PluginInstallFrame.optionPreview, 0.5, PluginInstallFrame.optionPreview:GetAlpha(), 0)
		UIFrameFadeIn(PluginInstallFrame.tutorialImage, 0.4, PluginInstallFrame.tutorialImage:GetAlpha(), 1)
		UIFrameFadeIn(PluginInstallFrame.Title, 0.4, PluginInstallFrame.Title:GetAlpha(), 1)
		UIFrameFadeIn(PluginInstallFrame.Prev, 0.4, PluginInstallFrame.Prev:GetAlpha(), 1)
		UIFrameFadeIn(PluginInstallFrame.Status, 0.4, PluginInstallFrame.Status:GetAlpha(), 1)
		UIFrameFadeIn(PluginInstallFrame.Next, 0.4, PluginInstallFrame.Next:GetAlpha(), 1)
		UIFrameFadeIn(PluginInstallFrame.Desc1, 0.4, PluginInstallFrame.Desc1:GetAlpha(), 1)
		UIFrameFadeIn(PluginInstallFrame.Desc2, 0.4, PluginInstallFrame.Desc2:GetAlpha(), 1)
		UIFrameFadeIn(PluginInstallFrame.Desc3, 0.4, PluginInstallFrame.Desc3:GetAlpha(), 1)
		UIFrameFadeIn(PluginInstallFrame.Desc4, 0.4, PluginInstallFrame.Desc4:GetAlpha(), 1)
		UIFrameFadeIn(PluginInstallFrame.SubTitle, 0.4, PluginInstallFrame.SubTitle:GetAlpha(), 1)
	end
end

local function resizeInstaller(reset)
	if reset then
		--* Defaults
		PluginInstallFrame:SetSize(450, 50)
		PluginInstallFrame.Desc1:ClearAllPoints()
		PluginInstallFrame.Desc1:SetPoint('TOPLEFT', _G.PluginInstallFrame, 'TOPLEFT', 20, -75)

		return
	end

	PluginInstallFrame:SetSize(1040, 520)
	PluginInstallFrame.Desc1:ClearAllPoints()
	PluginInstallFrame.Desc1:SetPoint('TOP', _G.PluginInstallFrame.SubTitle, 'BOTTOM', 0, -30)
end

local function resetButtonScripts()
	for i = 1, 4 do
		_G['PluginInstallFrame']['Option'..i]:SetScript('onEnter', nil)
		_G['PluginInstallFrame']['Option'..i]:SetScript('onLeave', nil)
	end
end

--* Installer Template
Engine.InstallerData = {
	Title = format('%s |cffFFD900%s|r', config.Title, L["Installation"]),
	Name = config.Title,
	tutorialImage = config.Logo,
	tutorialImageSize = { 256, 256 },
	tutorialImagePoint = { 0, 0 },
	Pages = {
		[1] = function()
			SetupOptionPreview()
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetFormattedText('|cffFFD900%s|r', L["Welcome"])

			PluginInstallFrame.Desc1:SetFormattedText('|cff4BEB2C%s|r', format('The %s installer will guide you through some steps and apply all the profile settings needed for the layout.', config.Title))
			PluginInstallFrame.Desc2:SetFormattedText('|cffFFFF00%s|r', format('%s layouts were made on a 1440p monitor using 0.56 UI Scale in ElvUI\'s options. If using another resolution/scale, you may need to adjust some frames size/location to ensure optimal placement for your setup.', config.Title))
			PluginInstallFrame.Desc3:SetFormattedText('|cffFF3300%s|r', "Please read the steps carefully before clicking any buttons!")

			if RoninUICharDB.SkipElvUIPrivateStep then
				RoninUICharDB.SkipElvUIPrivateStep = nil
				PI:SetPage(5, 4)
			elseif RoninUICharDB.SkipPlaterStep then
				RoninUICharDB.SkipPlaterStep = nil
				PI:SetPage(11, 10)
			end
		end,
		[2] = function()
			--* ElvUI Global Profile
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetText(format('|cffFFD900Global Profile (%s)|r', E.title))

			PluginInstallFrame.Desc1:SetText(format('|cff4BEB2C%s', 'This page will set up the global profile for ElvUI. The options in this profile will be shared across all characters on your account.'))
			PluginInstallFrame.Desc2:SetText(format('|cffFF3300Warning: |r%s', '|cffFFD900Be warned that this will overwrite your current global profile settings. There is no "undo" button, backup your WTF folder before proceeding.|r'))

			PluginInstallFrame.Option1:SetEnabled(true)
			PluginInstallFrame.Option1:SetScript('OnClick', function() SetupProfileButton('ElvUI', 'Global1') end)
			PluginInstallFrame.Option1:SetText(Engine.ProfileData.ElvUI.Global1ButtonText)
			PluginInstallFrame.Option1:Show()
		end,
		[3] = function()
			--* ElvUI General Profile
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetFormattedText('|cffFFD900%s|r', format('General Profile (%s)', E.title))

			PluginInstallFrame.Desc1:SetText(ElvUIProfileDescText())
			PluginInstallFrame.Desc2:SetText('|cff4BEB2CThis page will import the profile you clicked into ElvUI and make it the active profile. If the profile you click exists in the list of profiles in ElvUI, it will let you decide to overwrite or change the name of the selected profile.|r')

			PluginInstallFrame.Option1:SetEnabled(true)
			PluginInstallFrame.Option1:SetScript('OnClick', function() SetupProfileButton('ElvUI', 'Profile1') PluginInstallFrame.Desc1:SetText(ElvUIProfileDescText()) end)
			PluginInstallFrame.Option1:SetScript('onEnter', function() SetupOptionScripts('onEnter', Engine.ProfileData.ElvUI.Profile1Preview) end)
			PluginInstallFrame.Option1:SetScript('onLeave', function() SetupOptionScripts('onLeave') end)
			PluginInstallFrame.Option1:SetText(Engine.ProfileData.ElvUI.Profile1ButtonText)
			PluginInstallFrame.Option1:Show()

			PluginInstallFrame.Option2:SetEnabled(true)
			PluginInstallFrame.Option2:SetScript('OnClick', function() SetupProfileButton('ElvUI', 'Profile2') PluginInstallFrame.Desc1:SetText(ElvUIProfileDescText()) end)
			PluginInstallFrame.Option2:SetScript('onEnter', function() SetupOptionScripts('onEnter', Engine.ProfileData.ElvUI.Profile2Preview) end)
			PluginInstallFrame.Option2:SetScript('onLeave', function() SetupOptionScripts('onLeave') end)
			PluginInstallFrame.Option2:SetText(Engine.ProfileData.ElvUI.Profile2ButtonText)
			PluginInstallFrame.Option2:Show()
		end,
		[4] = function()
			--* ElvUI Private Profile
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetText(format('|cffFFD900Private Profile (%s)|r', E.title))

			PluginInstallFrame.Desc1:SetText(format('%sCurrent Private Profile:|r %s%s|r|n%s(|rElvUI Config %s>|r Profiles %s>|r Private Tab%s)|r', '|cffFFD900', '|cff5CE1E6', E.charSettings:GetCurrentProfile(), hexElvUIBlue, hexElvUIBlue, hexElvUIBlue, hexElvUIBlue))

			PluginInstallFrame.Option1:SetEnabled(true)
			PluginInstallFrame.Option1:SetScript('OnClick', function() SetupProfileButton('ElvUI', 'Private1') end)
			PluginInstallFrame.Option1:SetText(Engine.ProfileData.ElvUI.Private1ButtonText)
			PluginInstallFrame.Option1:Show()

			PluginInstallFrame.Option2:Hide()
		end,
		[5] = function()
			--* Details
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetFormattedText('|cffFFD900%s|r', 'Details')

			PluginInstallFrame.Desc1:SetText(GetDetailsDesc1Text())
			PluginInstallFrame.Desc2:SetText(GetDetailsDesc2Text())
			PluginInstallFrame.Desc3:SetText(GetDetailsDesc3Text())

			PluginInstallFrame.Option1:SetEnabled(E:IsAddOnEnabled('Details') and RoninUICharDB.isDualEmbedEnabled)
			PluginInstallFrame.Option1:SetScript('OnClick', function() SetupProfileButton('Details', 'Profile1') end)
			PluginInstallFrame.Option1:SetScript('onEnter', function() SetupOptionScripts('onEnter', Engine.ProfileData.Details.Profile1Preview) end)
			PluginInstallFrame.Option1:SetScript('onLeave', function() SetupOptionScripts('onLeave') end)
			PluginInstallFrame.Option1:SetText(Engine.ProfileData.Details.Profile1ButtonText)
			PluginInstallFrame.Option1:Show()

			PluginInstallFrame.Option2:SetEnabled(E:IsAddOnEnabled('Details') and not RoninUICharDB.isDualEmbedEnabled)
			PluginInstallFrame.Option2:SetScript('OnClick', function() if E:IsAddOnEnabled('Details') then RoninUICharDB.DetailsStepNeeded = true RoninUICharDB.isDualEmbedEnabled = true AS:SetOption('EmbedSystem', false) AS:SetOption('EmbedSystemDual', true) C_UI.Reload() end end)
			PluginInstallFrame.Option2:SetScript('onEnter', nil)
			PluginInstallFrame.Option2:SetScript('onLeave', function() SetupOptionScripts('onLeave') end)
			PluginInstallFrame.Option2:SetShown(not RoninUICharDB.isDualEmbedEnabled)
			PluginInstallFrame.Option2:SetText("Fix Conflict")
		end,
		[6] = function()
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetFormattedText("|cffFFD900%s|r", "BigWigs")

			PluginInstallFrame.Desc1:SetFormattedText("%sCurrent Profile:|r %s%s|r|n%s(|rBigWigs Config %s>|r Options %s>|r Profiles%s)|r", '|cffFFD900', '|cff5CE1E6', Engine.BigWigs:GetCurrentProfileName(), hexElvUIBlue, hexElvUIBlue, hexElvUIBlue, hexElvUIBlue)
			PluginInstallFrame.Desc2:SetFormattedText("|cffFFD900%s|r", format("This page will setup the BigWigs profile for %s", config.Title))

			PluginInstallFrame.Option1:SetEnabled(E:IsAddOnEnabled('BigWigs'))
			PluginInstallFrame.Option1:SetScript('OnClick', function() SetupProfileButton('BigWigs', 'Profile1') end)
			PluginInstallFrame.Option1:SetScript('onEnter', function() SetupOptionScripts('onEnter', Engine.ProfileData.BigWigs.Profile1Preview) end)
			PluginInstallFrame.Option1:SetScript('onLeave', function() SetupOptionScripts('onLeave') end)
			PluginInstallFrame.Option1:SetText('Setup BigWigs')
			PluginInstallFrame.Option1:Show()
		end,
		[7] = function()
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetFormattedText("|cffFFD900%s|r", "MRT")

			PluginInstallFrame.Desc1:SetText(MRTDesc1Text())
			PluginInstallFrame.Desc2:SetFormattedText("|cffFFD900%s|r", format("This page will setup the MRT profile for %s", config.Title))

			PluginInstallFrame.Option1:SetEnabled(E.Retail and E:IsAddOnEnabled('MRT'))
			PluginInstallFrame.Option1:SetScript('OnClick', function() RoninUICharDB.SkipMRTStep = true SetupProfileButton('MRT', 'Profile1') PluginInstallFrame.Desc1:SetText(MRTDesc1Text()) end)
			PluginInstallFrame.Option1:SetScript('onEnter', function() SetupOptionScripts('onEnter', Engine.ProfileData.MRT.Profile1Preview) end)
			PluginInstallFrame.Option1:SetScript('onLeave', function() SetupOptionScripts('onLeave') end)
			PluginInstallFrame.Option1:SetText("Setup MRT")
			PluginInstallFrame.Option1:Show()
		end,
		[8] = function()
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetFormattedText("|cffFFD900%s|r", "OmniBar")

			PluginInstallFrame.Desc1:SetFormattedText("|cffFFD900%s|r", format("This page will setup the OmniBar profile for %s", config.Title))

			PluginInstallFrame.Option1:SetEnabled(E:IsAddOnEnabled('OmniBar'))
			PluginInstallFrame.Option1:SetScript('OnClick', function() SetupProfileButton('OmniBar', 'Profile1') end)
			PluginInstallFrame.Option1:SetScript('onEnter', function() SetupOptionScripts('onEnter', Engine.ProfileData.OmniBar.Profile1Preview) end)
			PluginInstallFrame.Option1:SetScript('onLeave', function() SetupOptionScripts('onLeave') end)
			PluginInstallFrame.Option1:SetText(L["Setup OmniBar"])
			PluginInstallFrame.Option1:Show()
		end,
		[9] = function()
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetFormattedText("|cffFFD900%s|r", "OmniCD")

			PluginInstallFrame.Desc1:SetText(OmniCDDesc1Text())
			PluginInstallFrame.Desc2:SetFormattedText("|cffFFD900%s|r", format("This page will setup the OmniCD profile for %s", config.Title))

			PluginInstallFrame.Option1:SetEnabled(E:IsAddOnEnabled('OmniCD'))
			PluginInstallFrame.Option1:SetScript('OnClick', function() SetupProfileButton('OmniCD', 'Profile1') end)
			PluginInstallFrame.Option1:SetScript('onEnter', function() SetupOptionScripts('onEnter', Engine.ProfileData.OmniCD.Profile1Preview) end)
			PluginInstallFrame.Option1:SetScript('onLeave', function() SetupOptionScripts('onLeave') end)
			PluginInstallFrame.Option1:SetText(Engine.ProfileData.OmniCD.Profile1Title)
			PluginInstallFrame.Option1:Show()

			PluginInstallFrame.Option2:SetEnabled(E:IsAddOnEnabled('OmniCD'))
			PluginInstallFrame.Option2:SetScript('OnClick', function() SetupProfileButton('OmniCD', 'Profile2') end)
			PluginInstallFrame.Option2:SetScript('onEnter', function() SetupOptionScripts('onEnter', Engine.ProfileData.OmniCD.Profile2Preview) end)
			PluginInstallFrame.Option2:SetScript('onLeave', function() SetupOptionScripts('onLeave') end)
			PluginInstallFrame.Option2:SetText(Engine.ProfileData.OmniCD.Profile2Title)
			PluginInstallFrame.Option2:Show()
		end,
		[10] = function()
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetFormattedText("|cffFFD900%s|r", "Plater")

			PluginInstallFrame.Desc1:SetText(PlaterDesc1Text())
			PluginInstallFrame.Desc2:SetFormattedText("|cffFFD900%s|r", format("This page will setup the Plater profile for %s", config.Title))

			PluginInstallFrame.Option1:SetEnabled(E:IsAddOnEnabled('Plater'))
			PluginInstallFrame.Option1:SetScript('OnClick', function() end)
			PluginInstallFrame.Option1:SetScript('onEnter', function() SetupOptionScripts('onEnter', Engine.ProfileData.Plater.Profile1Preview) end)
			PluginInstallFrame.Option1:SetScript('onLeave', function() SetupOptionScripts('onLeave') end)
			PluginInstallFrame.Option1:SetText("Setup Plater")
			PluginInstallFrame.Option1:Show()
		end,
		[11] = function()
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetFormattedText("|cffFFD900%s|r", "WeakAuras")

			PluginInstallFrame.Desc1:SetFormattedText("|cffFFD900%s|r", 'This step will let you import my |cff4beb2cEssentials|r and class |cffFFFF00WeakAuras|r that I use for layout.')
			PluginInstallFrame.Desc2:SetText('|cffFFFF00Note:|r The Essentials pack only needs to be installed once per account.\nThe class packs for the rest of the classes can be imported for the rest \nof the classes from our section in the ElvUI options window.')

			PluginInstallFrame.Option1:SetEnabled(E:IsAddOnEnabled('WeakAuras'))
			PluginInstallFrame.Option1:SetScript('OnClick', function() SetupProfileButton('WeakAuras', 'Profile1', WeakAuraButtonText) end)
			WeakAuraButtonText('Profile1')
			PluginInstallFrame.Option1:Show()

			PluginInstallFrame.Option2:SetEnabled(E:IsAddOnEnabled('WeakAuras'))
			PluginInstallFrame.Option2:SetScript('OnClick',
			function()
				-- local importString = Engine.ProfileData.WeakAuras.CLASS[E.myclass]
				-- Engine.WeakAuras:SetupProfile(importString)
				SetupProfileButton('WeakAuras', 'Profile2', WeakAuraClassButtonText)
			end)

			WeakAuraClassButtonText()
			PluginInstallFrame.Option2:Show()
		end,
		[12] = function()
			resizeInstaller()
			resetButtonScripts()

			PluginInstallFrame.SubTitle:SetFormattedText("|cffFFD900%s|r", L["Installation Complete"])

			PluginInstallFrame.Desc1:SetFormattedText("|cffFFD900%s|r", "You have completed the installation process, please click 'Finished' to reload the UI.")
			PluginInstallFrame.Desc2:SetFormattedText("|cffFFD900%s|r", "Feel free to join our community Discord for support and feedback.")

			PluginInstallFrame.Option1:SetEnabled(true)
			PluginInstallFrame.Option1:SetScript('OnClick', function() E:StaticPopup_Show('RONINUI_EDITBOX', nil, nil, config.Discord) end)
			PluginInstallFrame.Option1:SetText(L["Discord"])
			PluginInstallFrame.Option1:Show()

			PluginInstallFrame.Option2:SetEnabled(true)
			PluginInstallFrame.Option2:SetScript('OnClick', function() RoninUICharDB.install_complete = config.Version resizeInstaller(true) C_UI.Reload() end)
			PluginInstallFrame.Option2:SetFormattedText("|cff4beb2c%s", L["Finished"])
			PluginInstallFrame.Option2:Show()
		end,
	},
	StepTitles = {
		[1] = L["Welcome"],
		[2] = 'Global Profile',
		[3] = 'General Profile',
		[4] = 'Private Profile',
		[5] = 'Details',
		[6] = 'BigWigs',
		[7] = 'MRT',
		[8] = 'OmniBar',
		[9] = 'OmniCD',
		[10] = 'Plater',
		[11] = 'WeakAuras',
		[12] = L["Installation Complete"],
	},
	StepTitlesColor = config.Installer.StepTitlesColor,
	StepTitlesColorSelected = config.Installer.StepTitlesColorSelected,
	StepTitleWidth = config.Installer.StepTitleWidth,
	StepTitleButtonWidth = config.Installer.StepTitleButtonWidth,
	StepTitleTextJustification = config.Installer.StepTitleTextJustification,
}
